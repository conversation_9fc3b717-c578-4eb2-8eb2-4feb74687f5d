use crate::traits::AuthEventMonitor;
use async_trait::async_trait;
use tokio::io::{AsyncBufReadExt, BufReader};
use tokio::process::Command;

pub struct MacOSMonitor;

#[async_trait]
impl AuthEventMonitor for MacOSMonitor {
    async fn monitor(&mut self) -> std::io::Result<()> {
        let mut child = Command::new("tail")
            .arg("-F")
            .arg("/var/log/system.log")
            .stdout(std::process::Stdio::piped())
            .spawn()?;

        let stdout = child.stdout.take().unwrap();
        let reader = BufReader::new(stdout);
        let mut lines = reader.lines();

        while let Some(line) = lines.next_line().await? {
            if line.contains("sshd") {
                if line.contains("session opened") {
                    println!("✅ SSH Login: {}", line);
                } else if line.contains("session closed") {
                    println!("🔒 SSH Logout: {}", line);
                } else if line.contains("Failed password") {
                    println!("❌ Failed login: {}", line);
                }
            }
        }

        Ok(())
    }
}
