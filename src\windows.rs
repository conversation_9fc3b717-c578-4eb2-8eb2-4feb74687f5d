use crate::traits::AuthEventMonitor;
use async_trait::async_trait;
use chrono::Utc;
use windows::{
    core::{Error as WinError, <PERSON>WS<PERSON>},
    Win32::Foundation::{ERROR_NO_MORE_ITEMS, HANDLE},
    Win32::System::EventLog::{
        EvtNext, EvtRender, EvtRenderEventXml, EvtSubscribe, EVT_HANDLE, EVT_RENDER_FLAGS,
        EVT_SUBSCRIBE_FLAGS, EVT_SUBSCRIBE_TO_FUTURE_EVENTS,
    },
    Win32::System::Threading::Sleep,
};

use std::{ffi::OsString, os::windows::ffi::OsStringExt, ptr};

pub struct WindowsMonitor;

#[async_trait]
impl AuthEventMonitor for WindowsMonitor {
    async fn monitor(&mut self) -> std::io::Result<()> {
        unsafe {
            let query = "*[System[Provider[@Name='Microsoft-Windows-Security-Auditing'] and (EventID=4624 or EventID=4634 or EventID=4625)]]";
            let query_utf16: Vec<u16> = query.encode_utf16().chain(std::iter::once(0)).collect();

            let handle = EvtSubscribe(
                None,
                None,
                PCWSTR::null(),
                PCWSTR(query_utf16.as_ptr()),
                None,
                None,
                None,
                EVT_SUBSCRIBE_TO_FUTURE_EVENTS.0,
            )?;

            if handle.0 == 0 {
                return Err(std::io::Error::last_os_error());
            }

            loop {
                let mut events = [];
                let mut returned = 0;

                EvtNext(handle, &mut events, 1000, 0, &mut returned)?;

                if returned > 0 {
                    let event_handle = events[0];
                    let xml = render_event_xml(event_handle)?;
                    let timestamp = Utc::now().to_rfc3339();

                    if xml.contains("<EventID>4624</EventID>") {
                        let (user, ip) = parse_user_ip(&xml);
                        println!("{} ✅ Login: user='{}' from IP={}", timestamp, user, ip);
                    } else if xml.contains("<EventID>4634</EventID>") {
                        let (user, ip) = parse_user_ip(&xml);
                        println!("{} 🔒 Logout: user='{}' from IP={}", timestamp, user, ip);
                    } else if xml.contains("<EventID>4625</EventID>") {
                        let (user, ip) = parse_user_ip(&xml);
                        println!(
                            "{} ❌ Failed login: user='{}' from IP={}",
                            timestamp, user, ip
                        );
                    }
                } else {
                    let err = WinError::from_win32();
                    if err.code().0 == ERROR_NO_MORE_ITEMS.0 as i32 {
                        Sleep(1000);
                        continue;
                    } else {
                        return Err(std::io::Error::from(err));
                    }
                }
            }
        }
    }
}

unsafe fn render_event_xml(event: EVT_HANDLE) -> std::io::Result<String> {
    let mut buffer_used = 0;
    let mut property_count = 0;

    // First call to get buffer size
    EvtRender(
        None,
        event,
        EvtRenderEventXml.0,
        0,
        None,
        &mut buffer_used,
        &mut property_count,
    );

    let mut buffer: Vec<u16> = vec![0; buffer_used as usize / 2];
    EvtRender(
        None,
        event,
        EvtRenderEventXml.0,
        buffer_used,
        Some(buffer.as_mut_ptr() as *mut _),
        &mut buffer_used,
        &mut property_count,
    )?;

    let os = OsString::from_wide(&buffer);
    Ok(os.to_string_lossy().into_owned())
}

fn parse_user_ip(xml: &str) -> (String, String) {
    let user = xml
        .split("<Data Name='TargetUserName'>")
        .nth(1)
        .and_then(|s| s.split("</Data>").next())
        .unwrap_or("unknown")
        .to_string();

    let ip = xml
        .split("<Data Name='IpAddress'>")
        .nth(1)
        .and_then(|s| s.split("</Data>").next())
        .unwrap_or("unknown")
        .to_string();

    (user, ip)
}
