use crate::traits::AuthEventMonitor;
use async_trait::async_trait;
use chrono::Utc;
use windows::{
    core::{Error as WinError, PCWSTR},
    Win32::{
        Foundation::ERROR_NO_MORE_ITEMS,
        System::EventLog::{
            EvtClose, EvtNext, EvtRender, EvtRenderEventXml, EvtSubscribe,
            EvtSubscribeToFutureEvents, EVT_HANDLE, EVT_SUBSCRIBE_FLAGS,
        },
    },
};

use std::{ffi::OsString, os::windows::ffi::OsStringExt};

pub struct WindowsMonitor;

#[async_trait]
impl AuthEventMonitor for WindowsMonitor {
    async fn monitor(&mut self) -> std::io::Result<()> {
        unsafe {
            let query = "*[System[Provider[@Name='Microsoft-Windows-Security-Auditing'] and (EventID=4624 or EventID=4634 or EventID=4625)]]";
            let query_utf16: Vec<u16> = query.encode_utf16().chain(std::iter::once(0)).collect();

            let handle = EvtSubscribe(
                None,
                None,
                PCWSTR::null(),
                PCWSTR(query_utf16.as_ptr()),
                None,
                None,
                None,
                EvtSubscribeToFutureEvents.0,
            )?;

            if handle.0 == 0 {
                return Err(std::io::Error::last_os_error());
            }

            println!("🔍 Monitoring Windows authentication events...");

            loop {
                // Allocate array for event handles
                let mut events: [isize; 10] = [0; 10];
                let mut returned = 0u32;

                // Try to get events
                let result = EvtNext(
                    handle,
                    &mut events,
                    1000, // 1 second timeout
                    0,    // flags
                    &mut returned,
                );

                if result.is_ok() && returned > 0 {
                    // Process each event
                    for i in 0..returned as usize {
                        let event_handle = EVT_HANDLE(events[i]);
                        if event_handle.0 != 0 {
                            match render_event_xml(event_handle) {
                                Ok(xml) => {
                                    let timestamp = Utc::now().to_rfc3339();
                                    process_event(&xml, &timestamp);
                                }
                                Err(e) => {
                                    eprintln!("Failed to render event XML: {}", e);
                                }
                            }

                            // Close the event handle to free memory
                            EvtClose(event_handle);
                        }
                    }
                } else {
                    // Check if it's just a timeout (no events available)
                    let err = WinError::from_win32();
                    if err.code().0 == ERROR_NO_MORE_ITEMS.0 as i32 {
                        // No events available, sleep and continue
                        tokio::time::sleep(tokio::time::Duration::from_millis(1000)).await;
                        continue;
                    } else if result.is_err() {
                        // Actual error occurred
                        return Err(std::io::Error::from(err));
                    }
                }
            }
        }
    }
}

unsafe fn render_event_xml(event: EVT_HANDLE) -> std::io::Result<String> {
    let mut buffer_used = 0;
    let mut property_count = 0;

    // First call to get buffer size
    EvtRender(
        None,
        event,
        EvtRenderEventXml.0,
        0,
        None,
        &mut buffer_used,
        &mut property_count,
    )?;

    let mut buffer: Vec<u16> = vec![0; buffer_used as usize / 2];
    EvtRender(
        None,
        event,
        EvtRenderEventXml.0,
        buffer_used,
        Some(buffer.as_mut_ptr() as *mut _),
        &mut buffer_used,
        &mut property_count,
    )?;

    let os = OsString::from_wide(&buffer);
    Ok(os.to_string_lossy().into_owned())
}

fn process_event(xml: &str, timestamp: &str) {
    if xml.contains("<EventID>4624</EventID>") {
        let (user, ip) = parse_user_ip(xml);
        println!("{} ✅ Login: user='{}' from IP={}", timestamp, user, ip);
    } else if xml.contains("<EventID>4634</EventID>") {
        let (user, ip) = parse_user_ip(xml);
        println!("{} 🔒 Logout: user='{}' from IP={}", timestamp, user, ip);
    } else if xml.contains("<EventID>4625</EventID>") {
        let (user, ip) = parse_user_ip(xml);
        println!(
            "{} ❌ Failed login: user='{}' from IP={}",
            timestamp, user, ip
        );
    }
}

fn parse_user_ip(xml: &str) -> (String, String) {
    let user = xml
        .split("<Data Name='TargetUserName'>")
        .nth(1)
        .and_then(|s| s.split("</Data>").next())
        .unwrap_or("unknown")
        .to_string();

    let ip = xml
        .split("<Data Name='IpAddress'>")
        .nth(1)
        .and_then(|s| s.split("</Data>").next())
        .unwrap_or("unknown")
        .to_string();

    (user, ip)
}
