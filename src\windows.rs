use crate::traits::AuthEventMonitor;
use async_trait::async_trait;
use chrono::Utc;
use windows::{
    core::{Error as Win<PERSON><PERSON><PERSON>, <PERSON>WSTR},
    Win32::{
        Foundation::ERROR_NO_MORE_ITEMS,
        System::EventLog::{EvtClose, EvtNext, EvtQuery, EvtRender, EvtRenderEventXml, EVT_HANDLE},
    },
};

use std::{ffi::OsString, os::windows::ffi::OsStringExt};

pub struct WindowsMonitor;

#[async_trait]
impl AuthEventMonitor for WindowsMonitor {
    async fn monitor(&mut self) -> std::io::Result<()> {
        unsafe {
            let query = "*[System[(EventID=4624 or EventID=4634 or EventID=4625)]]";
            let query_utf16: Vec<u16> = query.encode_utf16().chain(std::iter::once(0)).collect();

            let channel = "Security";
            let channel_utf16: Vec<u16> =
                channel.encode_utf16().chain(std::iter::once(0)).collect();

            println!("🔍 Attempting to query Windows Security events...");

            // Use EvtQuery with forward direction to continuously monitor
            let handle = EvtQuery(
                None,                           // Session (local machine)
                PCWSTR(channel_utf16.as_ptr()), // Path (channel name)
                PCWSTR(query_utf16.as_ptr()),   // Query
                0x200u32, // EvtQueryForwardDirection for continuous monitoring
            );

            let handle = match handle {
                Ok(h) => {
                    if h.0 == 0 {
                        return Err(std::io::Error::new(
                            std::io::ErrorKind::Other,
                            "EvtQuery returned null handle",
                        ));
                    }
                    println!("✅ Successfully opened query for Windows Security events");
                    h
                }
                Err(e) => {
                    eprintln!("❌ Failed to create query: {}", e);
                    return Err(std::io::Error::from(e));
                }
            };

            println!("🔍 Monitoring Windows authentication events...");
            println!("📡 Listening for login/logout events (EventID: 4624, 4634, 4625)");
            println!("🚫 Built-in system accounts will be filtered out");
            println!(
                "⏰ Started at: {}",
                chrono::Utc::now().format("%Y-%m-%d %H:%M:%S UTC")
            );
            println!("🔄 Application will continue running until stopped (Ctrl+C)");
            println!();

            let mut last_processed_time = std::time::Instant::now();

            loop {
                // Allocate array for event handles
                let mut events: [isize; 10] = [0; 10];
                let mut returned = 0u32;

                // Try to get events
                let result = EvtNext(
                    handle,
                    &mut events,
                    1000, // 1 second timeout
                    0,    // flags
                    &mut returned,
                );

                if result.is_ok() && returned > 0 {
                    // Process each event
                    for i in 0..returned as usize {
                        let event_handle = EVT_HANDLE(events[i]);
                        if event_handle.0 != 0 {
                            match render_event_xml(event_handle) {
                                Ok(xml) => {
                                    let timestamp = Utc::now().to_rfc3339();
                                    process_event(&xml, &timestamp);
                                }
                                Err(e) => {
                                    eprintln!("Failed to render event XML: {}", e);
                                }
                            }

                            // Close the event handle to free memory
                            let _ = EvtClose(event_handle);
                        }
                    }
                    last_processed_time = std::time::Instant::now();
                } else {
                    // Check if it's just a timeout or no more events available
                    let err = WinError::from_win32();
                    if err.code().0 == ERROR_NO_MORE_ITEMS.0 as i32 || result.is_ok() {
                        // No new events available - this is normal, just wait and continue
                        tokio::time::sleep(tokio::time::Duration::from_millis(2000)).await;

                        // Just reset the timer periodically to show we're still monitoring
                        if last_processed_time.elapsed().as_secs() > 300 {
                            println!(
                                "🔄 Still monitoring for authentication events... ({})",
                                chrono::Utc::now().format("%Y-%m-%d %H:%M:%S")
                            );
                            last_processed_time = std::time::Instant::now();
                        }
                        continue;
                    } else if result.is_err() {
                        // Only terminate on actual errors, not on "no more data"
                        eprintln!(
                            "⚠️ Event query error: {}, but continuing to monitor...",
                            err
                        );
                        tokio::time::sleep(tokio::time::Duration::from_millis(5000)).await;
                        continue;
                    }
                }
            }
        }
    }
}

unsafe fn render_event_xml(event: EVT_HANDLE) -> std::io::Result<String> {
    let mut buffer_used = 0;
    let mut property_count = 0;

    // First call to get buffer size - this call is expected to fail
    let _ = EvtRender(
        None,
        event,
        EvtRenderEventXml.0,
        0,
        None,
        &mut buffer_used,
        &mut property_count,
    );

    // Check if we got a valid buffer size
    if buffer_used == 0 {
        return Err(std::io::Error::new(
            std::io::ErrorKind::InvalidData,
            "Failed to get buffer size for event XML",
        ));
    }

    // Allocate buffer with the required size (buffer_used is in bytes)
    let mut buffer: Vec<u8> = vec![0; buffer_used as usize];
    let mut actual_used = buffer_used;

    // Second call to actually get the data
    EvtRender(
        None,
        event,
        EvtRenderEventXml.0,
        buffer_used,
        Some(buffer.as_mut_ptr() as *mut _),
        &mut actual_used,
        &mut property_count,
    )?;

    // Convert from UTF-16 bytes to String
    let utf16_slice = unsafe {
        std::slice::from_raw_parts(buffer.as_ptr() as *const u16, (actual_used as usize) / 2)
    };

    // Find the null terminator and create string
    let null_pos = utf16_slice
        .iter()
        .position(|&x| x == 0)
        .unwrap_or(utf16_slice.len());
    let os = OsString::from_wide(&utf16_slice[..null_pos]);
    Ok(os.to_string_lossy().into_owned())
}

fn process_event(xml: &str, timestamp: &str) {
    // Only process authentication-related events
    if xml.contains("<EventID>4624</EventID>") {
        let (user, ip) = parse_user_ip(xml);
        if !is_builtin_account(&user) {
            println!("{} ✅ Login: user='{}' from IP={}", timestamp, user, ip);
        } else {
            // Uncomment the line below to see filtered built-in accounts
            // eprintln!("{} 🔇 Filtered built-in login: user='{}'", timestamp, user);
        }
    } else if xml.contains("<EventID>4634</EventID>") {
        let (user, ip) = parse_user_ip(xml);
        if !is_builtin_account(&user) {
            println!("{} 🔒 Logout: user='{}' from IP={}", timestamp, user, ip);
        } else {
            // Uncomment the line below to see filtered built-in accounts
            // eprintln!("{} 🔇 Filtered built-in logout: user='{}'", timestamp, user);
        }
    } else if xml.contains("<EventID>4625</EventID>") {
        let (user, ip) = parse_user_ip(xml);
        if !is_builtin_account(&user) {
            println!(
                "{} ❌ Failed login: user='{}' from IP={}",
                timestamp, user, ip
            );
        } else {
            // Uncomment the line below to see filtered built-in accounts
            // eprintln!("{} 🔇 Filtered built-in failed login: user='{}'", timestamp, user);
        }
    }
    // Ignore other events silently
}

fn parse_user_ip(xml: &str) -> (String, String) {
    let user = xml
        .split("<Data Name='TargetUserName'>")
        .nth(1)
        .and_then(|s| s.split("</Data>").next())
        .unwrap_or("unknown")
        .to_string();

    let ip = xml
        .split("<Data Name='IpAddress'>")
        .nth(1)
        .and_then(|s| s.split("</Data>").next())
        .unwrap_or("unknown")
        .to_string();

    (user, ip)
}

fn is_builtin_account(username: &str) -> bool {
    // Convert to lowercase for case-insensitive comparison
    let username_lower = username.to_lowercase();

    // List of built-in Windows accounts to filter out
    let builtin_accounts = [
        // System accounts
        "system",
        "local service",
        "network service",
        "anonymous logon",
        "iusr",
        "iwam",
        // Service accounts (common patterns)
        "nt authority\\system",
        "nt authority\\local service",
        "nt authority\\network service",
        "nt authority\\anonymous logon",
        // Machine accounts (end with $)
        // We'll handle this with a pattern check below

        // Common service accounts
        "aspnet",
        "iis_iusrs",
        "iis_wpg",
        "sqlserveragent",
        "sqlserver",
        "mssqlserver",
        // Windows default accounts
        "defaultaccount",
        "wdagutilityaccount",
        "defaultuser0",
        "defaultuser100",
        // Common system users
        "dwm-1",
        "dwm-2",
        "dwm-3",
        "dwm-4",
        "dwm-5",
        "umfd-0",
        "umfd-1",
        "umfd-2",
        "umfd-3",
        "umfd-4",
        // Empty or unknown
        "unknown",
        "",
        "-",
    ];

    // Check against the list of built-in accounts
    if builtin_accounts.contains(&username_lower.as_str()) {
        return true;
    }

    // Check for machine accounts (computer accounts ending with $)
    if username_lower.ends_with('$') {
        return true;
    }

    // Check for NT AUTHORITY accounts
    if username_lower.starts_with("nt authority\\") {
        return true;
    }

    // Check for service accounts with common patterns
    if username_lower.starts_with("nt service\\") {
        return true;
    }

    // Check for virtual accounts
    if username_lower.starts_with("nt virtual machine\\") {
        return true;
    }

    // Check for container accounts
    if username_lower.starts_with("user manager\\") {
        return true;
    }

    // Check for accounts that look like GUIDs or have special patterns
    if username_lower.len() > 30
        && username_lower.contains('-')
        && username_lower
            .chars()
            .all(|c| c.is_ascii_hexdigit() || c == '-')
    {
        return true;
    }

    false
}
