use crate::traits::AuthEventMonitor;
use async_trait::async_trait;
use chrono::Utc;
use windows::{
    core::{Error as Win<PERSON><PERSON><PERSON>, <PERSON>WSTR},
    Win32::{
        Foundation::ERROR_NO_MORE_ITEMS,
        System::EventLog::{EvtClose, EvtNext, EvtQuery, EvtRender, EvtRenderEventXml, EVT_HANDLE},
    },
};

use std::{ffi::OsString, os::windows::ffi::OsStringExt};

pub struct WindowsMonitor;

#[async_trait]
impl AuthEventMonitor for WindowsMonitor {
    async fn monitor(&mut self) -> std::io::Result<()> {
        unsafe {
            let query = "*[System[(EventID=4624 or EventID=4634 or EventID=4625)]]";
            let query_utf16: Vec<u16> = query.encode_utf16().chain(std::iter::once(0)).collect();

            let channel = "Security";
            let channel_utf16: Vec<u16> =
                channel.encode_utf16().chain(std::iter::once(0)).collect();

            println!("🔍 Attempting to query Windows Security events...");

            // Use EvtQuery instead of EvtSubscribe for a simpler approach
            let handle = EvtQuery(
                None,                           // Session (local machine)
                PCWSTR(channel_utf16.as_ptr()), // Path (channel name)
                PCWSTR(query_utf16.as_ptr()),   // Query
                0x100u32,                       // EvtQueryReverseDirection to get newest first
            );

            let handle = match handle {
                Ok(h) => {
                    if h.0 == 0 {
                        return Err(std::io::Error::new(
                            std::io::ErrorKind::Other,
                            "EvtQuery returned null handle",
                        ));
                    }
                    println!("✅ Successfully opened query for Windows Security events");
                    h
                }
                Err(e) => {
                    eprintln!("❌ Failed to create query: {}", e);
                    return Err(std::io::Error::from(e));
                }
            };

            println!("🔍 Monitoring Windows authentication events...");
            let mut last_processed_time = std::time::Instant::now();

            loop {
                // Allocate array for event handles
                let mut events: [isize; 10] = [0; 10];
                let mut returned = 0u32;

                // Try to get events
                let result = EvtNext(
                    handle,
                    &mut events,
                    1000, // 1 second timeout
                    0,    // flags
                    &mut returned,
                );

                if result.is_ok() && returned > 0 {
                    // Process each event
                    for i in 0..returned as usize {
                        let event_handle = EVT_HANDLE(events[i]);
                        if event_handle.0 != 0 {
                            match render_event_xml(event_handle) {
                                Ok(xml) => {
                                    let timestamp = Utc::now().to_rfc3339();
                                    process_event(&xml, &timestamp);
                                }
                                Err(e) => {
                                    eprintln!("Failed to render event XML: {}", e);
                                }
                            }

                            // Close the event handle to free memory
                            let _ = EvtClose(event_handle);
                        }
                    }
                    last_processed_time = std::time::Instant::now();
                } else {
                    // Check if it's just a timeout (no events available)
                    let err = WinError::from_win32();
                    if err.code().0 == ERROR_NO_MORE_ITEMS.0 as i32 {
                        // No new events, sleep and continue
                        tokio::time::sleep(tokio::time::Duration::from_millis(2000)).await;

                        // Recreate query every 30 seconds to catch new events
                        if last_processed_time.elapsed().as_secs() > 30 {
                            let _ = EvtClose(handle);

                            let _new_handle = EvtQuery(
                                None,
                                PCWSTR(channel_utf16.as_ptr()),
                                PCWSTR(query_utf16.as_ptr()),
                                0x100u32,
                            )?;

                            // Update handle for next iteration
                            // Note: This is a simplified approach - in practice you'd want to
                            // track the last event timestamp and query from there
                            continue;
                        }
                        continue;
                    } else if result.is_err() {
                        // Actual error occurred
                        return Err(std::io::Error::from(err));
                    }
                }
            }
        }
    }
}

unsafe fn render_event_xml(event: EVT_HANDLE) -> std::io::Result<String> {
    let mut buffer_used = 0;
    let mut property_count = 0;

    // First call to get buffer size
    EvtRender(
        None,
        event,
        EvtRenderEventXml.0,
        0,
        None,
        &mut buffer_used,
        &mut property_count,
    )?;

    let mut buffer: Vec<u16> = vec![0; buffer_used as usize / 2];
    EvtRender(
        None,
        event,
        EvtRenderEventXml.0,
        buffer_used,
        Some(buffer.as_mut_ptr() as *mut _),
        &mut buffer_used,
        &mut property_count,
    )?;

    let os = OsString::from_wide(&buffer);
    Ok(os.to_string_lossy().into_owned())
}

fn process_event(xml: &str, timestamp: &str) {
    // Only process authentication-related events
    if xml.contains("<EventID>4624</EventID>") {
        let (user, ip) = parse_user_ip(xml);
        println!("{} ✅ Login: user='{}' from IP={}", timestamp, user, ip);
    } else if xml.contains("<EventID>4634</EventID>") {
        let (user, ip) = parse_user_ip(xml);
        println!("{} 🔒 Logout: user='{}' from IP={}", timestamp, user, ip);
    } else if xml.contains("<EventID>4625</EventID>") {
        let (user, ip) = parse_user_ip(xml);
        println!(
            "{} ❌ Failed login: user='{}' from IP={}",
            timestamp, user, ip
        );
    }
    // Ignore other events silently
}

fn parse_user_ip(xml: &str) -> (String, String) {
    let user = xml
        .split("<Data Name='TargetUserName'>")
        .nth(1)
        .and_then(|s| s.split("</Data>").next())
        .unwrap_or("unknown")
        .to_string();

    let ip = xml
        .split("<Data Name='IpAddress'>")
        .nth(1)
        .and_then(|s| s.split("</Data>").next())
        .unwrap_or("unknown")
        .to_string();

    (user, ip)
}
