use chrono::Utc;
use std::ffi::c_void;
use std::ptr::null_mut;
use windows::{
    core::w,
    Win32::{Foundation::*, System::EventLog::*},
};

fn main() -> windows::core::Result<()> {
    println!("▶️ Subscribing to Security events 4624/4625/4634...");

    unsafe {
        let sub = EvtSubscribe(
            None,
            None,
            w!("Security"),
            w!("*[System[(EventID=4624 or EventID=4625 or EventID=4634)]]"),
            None,
            Some(null_mut()),
            Some(Some(subscription_callback)),
            EvtSubscribeStartAtOldestRecord.0,
        )
        .expect("Failed to subscribe");
        if sub.0 == 0 {
            return Err(windows::core::Error::from_win32());
        }
        // Keep main thread alive:
        loop {
            std::thread::park();
        }
        // CloseHandle(sub); // unreachable
    }
}

#[no_mangle]
unsafe extern "system" fn subscription_callback(
    action: EVT_SUBSCRIBE_NOTIFY_ACTION,
    _ctx: *const c_void,
    h_evt: EVT_HANDLE,
) -> u32 {
    if action != EvtSubscribeActionDeliver {
        eprintln!("Subscribe error or other action: {:?}", action);
        return 1;
    }

    println!("Received event");

    unsafe {
        // Query the required buffer size - this call is expected to fail
        let mut needed = 0_u32;
        let mut props = 0_u32;

        // First call to get buffer size (expected to fail)
        let _ = EvtRender(
            None,                // Context (not needed for EventXml)
            h_evt,               // Event handle
            EvtRenderEventXml.0, // Render flags
            0,                   // Buffer size (0 for first call to get size)
            None,                // Buffer pointer (null for first call)
            &mut needed,         // Required buffer size (output)
            &mut props,          // Property count (output)
        );

        // Check if we got a valid buffer size
        if needed == 0 {
            eprintln!("Failed to get buffer size for event XML");
            return 1;
        }

        // Allocate buffer with the required size (in bytes, not u16s)
        let mut buf: Vec<u8> = vec![0; needed as usize];
        let mut actual_used = needed;

        // Second call to actually get the data
        if EvtRender(
            None,                             // Context
            h_evt,                            // Event handle
            EvtRenderEventXml.0,              // Render flags
            needed,                           // Buffer size
            Some(buf.as_mut_ptr() as *mut _), // Buffer pointer
            &mut actual_used,                 // Actual bytes used
            &mut props,                       // Property count
        )
        .is_err()
        {
            eprintln!("EvtRender failed: {:?}", GetLastError());
            return 1;
        }

        // Convert from UTF-16 bytes to String
        let utf16_slice =
            std::slice::from_raw_parts(buf.as_ptr() as *const u16, (actual_used as usize) / 2);

        // Find the null terminator and create string
        let null_pos = utf16_slice
            .iter()
            .position(|&x| x == 0)
            .unwrap_or(utf16_slice.len());
        let xml = String::from_utf16_lossy(&utf16_slice[..null_pos]);

        if let Some(msg) = parse_login_event(&xml) {
            println!("[{}] {}", Utc::now(), msg);
        }

        EvtClose(h_evt).expect("Failed to close event");
    }

    0
}

fn parse_login_event(xml: &str) -> Option<String> {
    println!("Xml {xml}");
    let kind = if xml.contains("<EventID>4624</EventID>") {
        "Login"
    } else if xml.contains("<EventID>4625</EventID>") {
        "FailedLogin"
    } else if xml.contains("<EventID>4634</EventID>") {
        "Logout"
    } else {
        return None;
    };

    let user = xml
        .split("<Data Name='TargetUserName'>")
        .nth(1)?
        .split("</Data>")
        .next()?;

    let ip = xml
        .split("<Data Name='IpAddress'>")
        .nth(1)
        .and_then(|s| s.split("</Data>").next())
        .unwrap_or("");

    Some(format!("Event={} user={} ip={}", kind, user, ip))
}
