use chrono::Utc;
use std::ffi::c_void;
use std::ptr::null_mut;
use windows::{
    core::w,
    Win32::{Foundation::*, System::EventLog::*},
};

fn main() -> windows::core::Result<()> {
    println!("▶️ Subscribing to Security events 4624/4625/4634...");

    unsafe {
        let sub = EvtSubscribe(
            None,
            None,
            w!("Security"),
            w!("*[System[(EventID=4624 or EventID=4625 or EventID=4634)]]"),
            None,
            Some(null_mut()),
            Some(Some(subscription_callback)),
            EvtSubscribeStartAtOldestRecord.0,
        )
        .expect("Failed to subscribe");
        if sub.0 == 0 {
            return Err(windows::core::Error::from_win32());
        }
        // Keep main thread alive:
        loop {
            std::thread::park();
        }
        // CloseHandle(sub); // unreachable
    }
}

#[no_mangle]
unsafe extern "system" fn subscription_callback(
    action: EVT_SUBSCRIBE_NOTIFY_ACTION,
    _ctx: *const c_void,
    h_evt: EVT_HANDLE,
) -> u32 {
    if action != EvtSubscribeActionDeliver {
        eprintln!("Subscribe error or other action: {:?}", action);
        return 1;
    }

    println!("Received event");

    unsafe {
        // Query the required buffer size
        let mut needed = 0_u32;
        let mut props = 0_u32;
        EvtRender(
            Some(EVT_HANDLE(0)),
            h_evt,
            EvtRenderEventXml.0,
            0,
            None,
            &mut needed,
            &mut props,
        )
        .expect("Failed to render event");

        // Allocate buffer and render XML
        let mut buf: Vec<u16> = vec![0; needed as usize];
        if EvtRender(
            Some(EVT_HANDLE(0)),
            h_evt,
            EvtRenderEventXml.0,
            needed,
            Some(buf.as_mut_ptr() as *mut _),
            &mut needed,
            &mut props,
        )
        .is_err()
        {
            eprintln!("EvtRender failed: {:?}", GetLastError());
            return 1;
        }

        let xml = String::from_utf16_lossy(&buf);
        if let Some(msg) = parse_login_event(&xml) {
            println!("[{}] {}", Utc::now(), msg);
        }

        EvtClose(h_evt).expect("Failed to close event");
    }

    0
}

fn parse_login_event(xml: &str) -> Option<String> {
    println!("Xml {xml}");
    let kind = if xml.contains("<EventID>4624</EventID>") {
        "Login"
    } else if xml.contains("<EventID>4625</EventID>") {
        "FailedLogin"
    } else if xml.contains("<EventID>4634</EventID>") {
        "Logout"
    } else {
        return None;
    };

    let user = xml
        .split("<Data Name='TargetUserName'>")
        .nth(1)?
        .split("</Data>")
        .next()?;

    let ip = xml
        .split("<Data Name='IpAddress'>")
        .nth(1)
        .and_then(|s| s.split("</Data>").next())
        .unwrap_or("");

    Some(format!("Event={} user={} ip={}", kind, user, ip))
}
