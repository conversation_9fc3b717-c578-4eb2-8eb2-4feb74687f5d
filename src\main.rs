#[cfg(target_os = "linux")]
mod linux;
#[cfg(target_os = "macos")]
mod macos;
#[cfg(windows)]
mod windows;

mod traits;
use cfg_if::cfg_if;
use traits::AuthEventMonitor;

#[tokio::main]
async fn main() -> std::io::Result<()> {
    cfg_if! {
        if #[cfg(target_os = "linux")] {
            let mut monitor = linux::LinuxMonitor;
        } else if #[cfg(target_os = "macos")] {
            let mut monitor = macos::MacOSMonitor;
        } else if #[cfg(target_os = "windows")] {
            let mut monitor = windows::WindowsMonitor;
        } else {
            compile_error!("Unsupported OS");
        }
    }

    monitor.monitor().await
}
