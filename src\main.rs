use chrono::Utc;
use std::ffi::c_void;
use std::ptr::null_mut;
use windows::{
    core::w,
    Win32::{Foundation::*, System::EventLog::*},
};

fn main() -> windows::core::Result<()> {
    println!("▶️ Subscribing to Security events 4624/4625/4634...");

    unsafe {
        let sub = EvtSubscribe(
            None,
            None,
            w!("Security"),
            w!("*[System[(EventID=4624 or EventID=4625 or EventID=4634)]]"),
            None,
            Some(null_mut()),
            Some(Some(subscription_callback)),
            EvtSubscribeStartAtOldestRecord.0,
        )
        .expect("Failed to subscribe");
        if sub.0 == 0 {
            return Err(windows::core::Error::from_win32());
        }
        // Keep main thread alive:
        loop {
            std::thread::park();
        }
        // CloseHandle(sub); // unreachable
    }
}

#[no_mangle]
unsafe extern "system" fn subscription_callback(
    action: EVT_SUBSCRIBE_NOTIFY_ACTION,
    _ctx: *const c_void,
    h_evt: EVT_HANDLE,
) -> u32 {
    if action != EvtSubscribeActionDeliver {
        eprintln!("Subscribe error or other action: {:?}", action);
        return 1;
    }

    unsafe {
        // Query the required buffer size - this call is expected to fail
        let mut needed = 0_u32;
        let mut props = 0_u32;

        // First call to get buffer size (expected to fail)
        let _ = EvtRender(
            None,                // Context (not needed for EventXml)
            h_evt,               // Event handle
            EvtRenderEventXml.0, // Render flags
            0,                   // Buffer size (0 for first call to get size)
            None,                // Buffer pointer (null for first call)
            &mut needed,         // Required buffer size (output)
            &mut props,          // Property count (output)
        );

        // Check if we got a valid buffer size
        if needed == 0 {
            eprintln!("Failed to get buffer size for event XML");
            return 1;
        }

        // Allocate buffer with the required size (in bytes, not u16s)
        let mut buf: Vec<u8> = vec![0; needed as usize];
        let mut actual_used = needed;

        // Second call to actually get the data
        if EvtRender(
            None,                             // Context
            h_evt,                            // Event handle
            EvtRenderEventXml.0,              // Render flags
            needed,                           // Buffer size
            Some(buf.as_mut_ptr() as *mut _), // Buffer pointer
            &mut actual_used,                 // Actual bytes used
            &mut props,                       // Property count
        )
        .is_err()
        {
            eprintln!("EvtRender failed: {:?}", GetLastError());
            return 1;
        }

        // Convert from UTF-16 bytes to String
        let utf16_slice =
            std::slice::from_raw_parts(buf.as_ptr() as *const u16, (actual_used as usize) / 2);

        // Find the null terminator and create string
        let null_pos = utf16_slice
            .iter()
            .position(|&x| x == 0)
            .unwrap_or(utf16_slice.len());
        let xml = String::from_utf16_lossy(&utf16_slice[..null_pos]);

        // Process the event with filtering
        let timestamp = Utc::now().to_rfc3339();
        process_authentication_event(&xml, &timestamp);

        EvtClose(h_evt).expect("Failed to close event");
    }

    0
}

fn process_authentication_event(xml: &str, timestamp: &str) {
    // Only process authentication-related events
    if xml.contains("<EventID>4624</EventID>") {
        let (user, ip, logon_type) = parse_user_ip_logontype(xml);
        let logon_desc = get_logon_type_description(&logon_type);

        if !is_builtin_account(&user) && is_real_user_logon_type(&logon_type) {
            println!(
                "{} ✅ Login: user='{}' from IP={} ({})",
                timestamp, user, ip, logon_desc
            );
        } else {
            // Uncomment the lines below to see filtered events
            // if is_builtin_account(&user) {
            //     eprintln!("{} 🔇 Filtered built-in login: user='{}' ({})", timestamp, user, logon_desc);
            // } else {
            //     eprintln!("{} 🔇 Filtered logon type: user='{}' type={} ({})", timestamp, user, logon_type, logon_desc);
            // }
        }
    } else if xml.contains("<EventID>4634</EventID>") {
        let (user, ip, logon_type) = parse_user_ip_logontype(xml);
        let logon_desc = get_logon_type_description(&logon_type);

        if !is_builtin_account(&user) && is_real_user_logon_type(&logon_type) {
            println!(
                "{} 🔒 Logout: user='{}' from IP={} ({})",
                timestamp, user, ip, logon_desc
            );
        } else {
            // Uncomment the lines below to see filtered events
            // if is_builtin_account(&user) {
            //     eprintln!("{} 🔇 Filtered built-in logout: user='{}' ({})", timestamp, user, logon_desc);
            // } else {
            //     eprintln!("{} 🔇 Filtered logon type: user='{}' type={} ({})", timestamp, user, logon_type, logon_desc);
            // }
        }
    } else if xml.contains("<EventID>4625</EventID>") {
        let (user, ip, logon_type) = parse_user_ip_logontype(xml);
        let logon_desc = get_logon_type_description(&logon_type);

        if !is_builtin_account(&user) && is_real_user_logon_type(&logon_type) {
            println!(
                "{} ❌ Failed login: user='{}' from IP={} ({})",
                timestamp, user, ip, logon_desc
            );
        } else {
            // Uncomment the lines below to see filtered events
            // if is_builtin_account(&user) {
            //     eprintln!("{} 🔇 Filtered built-in failed login: user='{}' ({})", timestamp, user, logon_desc);
            // } else {
            //     eprintln!("{} 🔇 Filtered logon type: user='{}' type={} ({})", timestamp, user, logon_type, logon_desc);
            // }
        }
    }
    // Ignore other events silently
}

fn parse_user_ip_logontype(xml: &str) -> (String, String, String) {
    let user = xml
        .split("<Data Name='TargetUserName'>")
        .nth(1)
        .and_then(|s| s.split("</Data>").next())
        .unwrap_or("unknown")
        .to_string();

    let ip = xml
        .split("<Data Name='IpAddress'>")
        .nth(1)
        .and_then(|s| s.split("</Data>").next())
        .unwrap_or("unknown")
        .to_string();

    let logon_type = xml
        .split("<Data Name='LogonType'>")
        .nth(1)
        .and_then(|s| s.split("</Data>").next())
        .unwrap_or("unknown")
        .to_string();

    (user, ip, logon_type)
}

fn is_builtin_account(username: &str) -> bool {
    // Convert to lowercase for case-insensitive comparison
    let username_lower = username.to_lowercase();

    // List of built-in Windows accounts to filter out
    let builtin_accounts = [
        // System accounts
        "system",
        "local service",
        "network service",
        "anonymous logon",
        "iusr",
        "iwam",
        // Service accounts (common patterns)
        "nt authority\\system",
        "nt authority\\local service",
        "nt authority\\network service",
        "nt authority\\anonymous logon",
        // Common service accounts
        "aspnet",
        "iis_iusrs",
        "iis_wpg",
        "sqlserveragent",
        "sqlserver",
        "mssqlserver",
        // Windows default accounts
        "defaultaccount",
        "wdagutilityaccount",
        "defaultuser0",
        "defaultuser100",
        // Common system users
        "dwm-1",
        "dwm-2",
        "dwm-3",
        "dwm-4",
        "dwm-5",
        "umfd-0",
        "umfd-1",
        "umfd-2",
        "umfd-3",
        "umfd-4",
        // Empty or unknown
        "unknown",
        "",
        "-",
    ];

    // Check against the list of built-in accounts
    if builtin_accounts.contains(&username_lower.as_str()) {
        return true;
    }

    // Check for machine accounts (computer accounts ending with $)
    if username_lower.ends_with('$') {
        return true;
    }

    // Check for NT AUTHORITY accounts
    if username_lower.starts_with("nt authority\\") {
        return true;
    }

    // Check for service accounts with common patterns
    if username_lower.starts_with("nt service\\") {
        return true;
    }

    // Check for virtual accounts
    if username_lower.starts_with("nt virtual machine\\") {
        return true;
    }

    // Check for container accounts
    if username_lower.starts_with("user manager\\") {
        return true;
    }

    // Check for accounts that look like GUIDs or have special patterns
    if username_lower.len() > 30
        && username_lower.contains('-')
        && username_lower
            .chars()
            .all(|c| c.is_ascii_hexdigit() || c == '-')
    {
        return true;
    }

    false
}

fn is_real_user_logon_type(logon_type: &str) -> bool {
    match logon_type {
        "2" => true,  // Interactive (console logon)
        "3" => true,  // Network (SMB, file shares, etc.)
        "10" => true, // RemoteInteractive (RDP, Terminal Services)
        "11" => true, // CachedInteractive (cached domain credentials)

        // Filter out these logon types:
        "0" => false,  // System (used only by the System account)
        "1" => false,  // Interactive (not used in practice)
        "4" => false,  // Batch (scheduled tasks)
        "5" => false,  // Service (Windows services)
        "7" => false,  // Unlock (workstation unlock)
        "8" => false,  // NetworkCleartext (IIS with basic auth)
        "9" => false,  // NewCredentials (RunAs with /netonly)
        "12" => false, // CachedRemoteInteractive (cached RDP)
        "13" => false, // CachedUnlock (cached unlock)

        // Unknown logon types - be conservative and filter out
        _ => false,
    }
}

fn get_logon_type_description(logon_type: &str) -> &'static str {
    match logon_type {
        "0" => "System",
        "1" => "Interactive",
        "2" => "Interactive",
        "3" => "Network",
        "4" => "Batch",
        "5" => "Service",
        "7" => "Unlock",
        "8" => "NetworkCleartext",
        "9" => "NewCredentials",
        "10" => "RemoteInteractive",
        "11" => "CachedInteractive",
        "12" => "CachedRemoteInteractive",
        "13" => "CachedUnlock",
        _ => "Unknown",
    }
}
