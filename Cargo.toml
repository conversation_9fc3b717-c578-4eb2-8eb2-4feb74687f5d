[package]
name = "auth_event_monitor"
version = "0.1.0"
edition = "2021"

[dependencies]
tokio = { version = "1.37", features = ["full"] }
regex = "1"
futures = "0.3"
async-trait = "0.1"
cfg-if = "1.0"
chrono = { version = "0.4", features = ["alloc"] }

[target.'cfg(target_os = "linux")'.dependencies]
inotify = "0.11"

[target.'cfg(windows)'.dependencies]
windows = { version = "0.59", features = [
  "Win32_System_EventLog",
  "Win32_Foundation",
  "Win32_System_Threading",
] }
