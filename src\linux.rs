use crate::traits::AuthEventMonitor;
use async_trait::async_trait;
use chrono::{Datelike, TimeZone, Utc};
use futures::StreamExt;
use inotify::{Inotify, WatchMask};
use regex::Regex;
use std::collections::HashMap;
use tokio::fs::File;
use tokio::io::{AsyncBufReadExt, AsyncSeekExt, BufReader, SeekFrom};

pub struct LinuxMonitor;

#[async_trait]
impl AuthEventMonitor for LinuxMonitor {
    async fn monitor(&mut self) -> std::io::Result<()> {
        let path = "/var/log/auth.log";
        let mut file = File::open(path).await?;
        file.seek(SeekFrom::End(0)).await?;
        let mut reader = BufReader::new(file);
        reader.seek(SeekFrom::End(0)).await?;
        let mut lines = reader.lines();

        let inotify = Inotify::init()?;
        inotify.watches().add(path, WatchMask::MODIFY)?;
        let mut buffer = [0u8; 4096];
        let mut event_stream = inotify.into_event_stream(&mut buffer)?;

        let re_failed = Regex::new(
            r"sshd.*Failed password for (invalid user )?(?P<user>\w+) from (?P<ip>[\d\.]+)",
        )
        .unwrap();
        let re_accepted =
            Regex::new(r"sshd.*Accepted \w+ for (?P<user>\w+) from (?P<ip>[\d\.]+)").unwrap();
        let re_login = Regex::new(r"sshd.*session opened for user (?P<user>\w+)").unwrap();
        let re_logout = Regex::new(r"sshd.*session closed for user (?P<user>\w+)").unwrap();

        let mut active_ips: HashMap<String, String> = HashMap::new();

        while let Some(Ok(_)) = event_stream.next().await {
            while let Ok(Some(line)) = lines.next_line().await {
                if let Some(timestamp) = extract_utc_timestamp(&line) {
                    if let Some(caps) = re_failed.captures(&line) {
                        let user = &caps["user"];
                        let ip = &caps["ip"];
                        println!(
                            "{} ❌ Failed SSH login: user '{}' from IP {}",
                            timestamp, user, ip
                        );
                    } else if let Some(caps) = re_accepted.captures(&line) {
                        active_ips.insert(caps["user"].to_string(), caps["ip"].to_string());
                    } else if let Some(caps) = re_login.captures(&line) {
                        let user = &caps["user"];
                        let ip = active_ips.get(user).map_or("unknown", |ip| ip.as_str());
                        println!("{} ✅ SSH Login: user '{}' from IP {}", timestamp, user, ip);
                    } else if let Some(caps) = re_logout.captures(&line) {
                        let user = &caps["user"];
                        let ip = active_ips
                            .remove(user)
                            .unwrap_or_else(|| "unknown".to_string());
                        println!(
                            "{} 🔒 SSH Logout: user '{}' from IP {}",
                            timestamp, user, ip
                        );
                    }
                }
            }
        }

        Ok(())
    }
}

fn extract_utc_timestamp(line: &str) -> Option<String> {
    let re =
        Regex::new(r"^(?P<month>\w{3})\s+(?P<day>\d{1,2})\s+(?P<time>\d{2}:\d{2}:\d{2})").ok()?;
    let caps = re.captures(line)?;
    let month_str = caps.name("month")?.as_str();
    let day = caps.name("day")?.as_str().parse::<u32>().ok()?;
    let time_str = caps.name("time")?.as_str();
    let year = Utc::now().year();
    let full_str = format!("{} {} {} {}", year, month_str, day, time_str);
    let naive = chrono::NaiveDateTime::parse_from_str(&full_str, "%Y %b %d %H:%M:%S").ok()?;
    Some(Utc.from_utc_datetime(&naive).to_rfc3339())
}
